import google.generativeai as genai
import os
import random
from PIL import Image # 用于处理图像，如果需要加载本地图像作为输入

# --- 配置 Gemini API ---
# 请将 'YOUR_API_KEY' 替换为您的实际 Gemini API 密钥
API_KEY = "AIzaSyCeJFDSzuefKRA2tb3l1b7vUipBQc9CxGI"
genai.configure(api_key=API_KEY)

# 请选择合适的模型名称。对于图像生成，通常会使用类似于 "gemini-pro-vision" 或其他支持图像输入/输出的模型。
# 查阅 Gemini API 文档以获取最新的模型名称。
MODEL_NAME = "imagen-4.0-generate-preview-06-06" # 示例模型名称，请根据实际情况调整

def generate_image_with_prompt(original_image_path, base_prompt):
    """
    使用 Gemini API 根据给定提示词生成图片。

    Args:
        original_image_path (str): 原始图片的本地路径。
        base_prompt (str): 用于图像生成的提示词。
    """
    try:
        # 加载原始图像
        # 如果您的API模型直接支持图像URL作为输入，您也可以考虑直接传递URL
        img = Image.open("dog.jpg")  # 确保此路径正确，或者将图片放在脚本同目录下

        # 构建完整的提示词
        # 这里的提示词直接使用用户提供的，让AI自由发挥
        full_prompt = base_prompt

        print(f"生成的提示词: {full_prompt}")

        # 初始化模型
        model = genai.GenerativeModel(MODEL_NAME)

        # 发送生成请求
        # 对于多模态模型，您可能需要将图像和文本提示一起传递
        # 具体的传递方式请参考您使用的Gemini模型版本的官方文档
        response = model.generate_content([full_prompt, img]) # 示例：同时传递文本和图像

        # 检查并处理响应
        if response.candidates:
            for candidate in response.candidates:
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    for i, part in enumerate(candidate.content.parts):
                        if part.mime_type and part.mime_type.startswith('image/'):
                            # 假设返回的是图像数据，保存图像
                            # 注意：实际保存图像的方式可能因API返回的数据格式而异
                            # 这里是一个示例，您可能需要根据API的实际响应进行调整
                            image_data = part.blob.data
                            output_filename = f"generated_image_{random.randint(1000, 9999)}.png"
                            with open(output_filename, "wb") as f:
                                f.write(image_data)
                            print(f"成功生成并保存图片: {output_filename}")
                        elif hasattr(part, 'text'):
                            print(f"生成的文本描述: {part.text}")
                else:
                    print("无法从响应中获取图像内容或文本内容。")
        else:
            print("未能成功生成图片，响应中没有候选内容。")
            if hasattr(response, 'prompt_feedback') and response.prompt_feedback.block_reason:
                print(f"生成被阻止，原因: {response.prompt_feedback.block_reason.name}")

    except Exception as e:
        print(f"发生错误: {e}")

# --- 主程序 ---
if __name__ == "__main__":
    original_image_path = "dog.jpg" # 确保此路径正确，或者将图片放在脚本同目录下
    user_prompt = (
        "这张图片里是一只狗。请你将它更换服装、帽子、手上的物品等。"
        "请保持狗的品种不变，并生成一张高质量的艺术图片。"
        "图片风格可以是卡通、写实或任何有趣的风格，由AI自由发挥。"
    )

    if not os.path.exists(original_image_path):
        print(f"错误: 找不到原始图片文件 '{original_image_path}'。请确保文件存在。")
    else:
        num_generations = 3 # 您可以设置想要重复生成的次数
        for i in range(num_generations):
            print(f"\n--- 第 {i+1} 次生成 ---")
            generate_image_with_prompt(original_image_path, user_prompt)
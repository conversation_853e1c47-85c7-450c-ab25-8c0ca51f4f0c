import google.generativeai as genai

# 配置API密钥
API_KEY = "AIzaSyCeJFDSzuefKRA2tb3l1b7vUipBQc9CxGI"
genai.configure(api_key=API_KEY)

def list_available_models():
    """列出所有可用的模型"""
    try:
        print("🔍 正在获取可用模型列表...")
        print("=" * 60)
        
        models = genai.list_models()
        
        text_models = []
        vision_models = []
        other_models = []
        
        for model in models:
            model_name = model.name if hasattr(model, 'name') else str(model)
            supported_methods = []
            if hasattr(model, 'supported_generation_methods'):
                supported_methods = [method.name if hasattr(method, 'name') else str(method) for method in model.supported_generation_methods]
            
            print(f"📋 模型: {model_name}")
            print(f"   支持的方法: {', '.join(supported_methods)}")
            
            # 分类模型
            if 'generateContent' in supported_methods:
                if 'vision' in model_name.lower() or 'pro-vision' in model_name.lower():
                    vision_models.append(model_name)
                elif 'gemini' in model_name.lower():
                    text_models.append(model_name)
                else:
                    other_models.append(model_name)
            
            print("-" * 40)
        
        print("\n📊 模型分类总结:")
        print("=" * 60)
        
        if text_models:
            print("🔤 文本生成模型:")
            for model in text_models:
                print(f"   - {model}")
        
        if vision_models:
            print("\n👁️ 视觉/多模态模型:")
            for model in vision_models:
                print(f"   - {model}")
        
        if other_models:
            print("\n🔧 其他模型:")
            for model in other_models:
                print(f"   - {model}")
        
        # 推荐适合的模型
        print("\n💡 推荐用于图像处理的模型:")
        if vision_models:
            recommended = vision_models[0]
            print(f"   建议使用: {recommended}")
            return recommended
        elif text_models:
            recommended = text_models[0]
            print(f"   备选方案: {recommended} (仅支持文本)")
            return recommended
        else:
            print("   ❌ 没有找到合适的模型")
            return None
            
    except Exception as e:
        print(f"❌ 获取模型列表失败: {e}")
        return None

if __name__ == "__main__":
    recommended_model = list_available_models()
    
    if recommended_model:
        print(f"\n✅ 建议在dog.py中使用模型: {recommended_model}")
    else:
        print("\n❌ 无法找到合适的模型")

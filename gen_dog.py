import google.generativeai as genai
import os
import time
from PIL import Image
import io
import sys

# ====== API密钥配置 ======
API_KEY = None

# 尝试从多个来源获取密钥
possible_sources = [
    os.getenv('GEMINI_API_KEY'),
    "your_actual_key_here"  # 可在此直接硬编码密钥（不推荐）
]

for key in possible_sources:
    if key and not key.startswith("AIzaSy"):
        API_KEY = key
        break

if not API_KEY:
    print("错误：未找到有效的API密钥")
    print("请选择以下设置方式：")
    print("1. 在代码中直接设置 API_KEY = 'your_key'")
    print("2. 在cmd执行: setx GEMINI_API_KEY your_key")
    sys.exit(1)

genai.configure(api_key=API_KEY)

# ====== 图片生成部分 ======
def generate_dog_image():
    prompt = """这张图片里是一只狗。请你将它更换服装、帽子、手上的物品等。
请保持狗的品种不变，并生成一张高质量的艺术图片。
图片风格可以是卡通、写实或任何有趣的风格，由AI自由发挥。"""
    
    try:
        with open("dog.jpg", "rb") as f:
            img_data = f.read()
        
        model = genai.GenerativeModel('gemini-1.5-pro-latest')
        response = model.generate_content(
            ["根据提示修改图片", {"mime_type": "image/jpeg", "data": img_data}, prompt],
            generation_config={"temperature": 0.7}
        )
        
        if response.parts and hasattr(response.parts[0], "data"):
            Image.open(io.BytesIO(response.parts[0].data)).save("output.png")
            print("图片生成成功：output.png")
        else:
            print("生成失败，返回：", response.text)
    except Exception as e:
        print("发生错误：", str(e))

if __name__ == "__main__":
    generate_dog_image()
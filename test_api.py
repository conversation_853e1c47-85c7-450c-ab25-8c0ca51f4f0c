import google.generativeai as genai
import sys

# 配置API密钥
API_KEY = "AIzaSyCeJFDSzuefKRA2tb3l1b7vUipBQc9CxGI"

def test_api_key():
    """测试API密钥是否有效"""
    try:
        # 配置API
        genai.configure(api_key=API_KEY)
        print("✅ API密钥配置成功")
        
        # 列出可用的模型
        print("\n📋 正在获取可用模型列表...")
        models = genai.list_models()
        
        available_models = []
        for model in models:
            available_models.append(model.name)
            print(f"  - {model.name}")
        
        if not available_models:
            print("❌ 没有找到可用的模型")
            return False
            
        print(f"\n✅ 找到 {len(available_models)} 个可用模型")
        
        # 测试一个简单的文本生成
        print("\n🧪 测试文本生成功能...")
        
        # 尝试使用gemini-pro模型进行简单测试
        try:
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content("Hello, please respond with 'API test successful'")
            
            if response.text:
                print(f"✅ 文本生成测试成功!")
                print(f"📝 响应内容: {response.text}")
                return True
            else:
                print("❌ 文本生成测试失败: 没有收到响应内容")
                return False
                
        except Exception as e:
            print(f"❌ 文本生成测试失败: {e}")
            
            # 如果gemini-pro不可用，尝试其他模型
            if available_models:
                print(f"\n🔄 尝试使用其他模型: {available_models[0]}")
                try:
                    model = genai.GenerativeModel(available_models[0])
                    response = model.generate_content("Hello")
                    if response.text:
                        print(f"✅ 使用 {available_models[0]} 测试成功!")
                        print(f"📝 响应内容: {response.text}")
                        return True
                except Exception as e2:
                    print(f"❌ 使用 {available_models[0]} 也失败了: {e2}")
            
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        
        # 检查是否是网络连接问题
        if "ServiceUnavailable" in str(e) or "failed to connect" in str(e):
            print("\n🌐 这看起来像是网络连接问题:")
            print("   - 请检查网络连接")
            print("   - 如果在中国大陆，可能需要VPN来访问Google服务")
            print("   - 检查防火墙设置")
        elif "INVALID_API_KEY" in str(e) or "API_KEY_INVALID" in str(e):
            print("\n🔑 API密钥问题:")
            print("   - 请检查API密钥是否正确")
            print("   - 确认API密钥是否已启用")
            print("   - 检查API配额是否充足")
        
        return False

def main():
    print("🔍 开始测试Google Gemini API密钥...")
    print("=" * 50)
    
    success = test_api_key()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 API密钥测试通过! 可以正常使用。")
    else:
        print("💥 API密钥测试失败! 请检查上述错误信息。")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
